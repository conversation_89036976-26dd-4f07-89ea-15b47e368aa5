#!/usr/bin/env node
import { DingTalkMCPServer } from './DingTalkMCPServer.js';
/**
 * 验证必需的环境变量
 */
function validateEnvironment() {
    const requiredEnvVars = ['DINGTALK_Client_ID', 'DINGTALK_Client_Secret'];
    const missingVars = [];
    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            missingVars.push(envVar);
        }
    }
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(varName => {
            console.error(`   - ${varName}`);
        });
        console.error('\n📝 Please set these environment variables before running the server.');
        console.error('\n💡 Example:');
        console.error('   export DINGTALK_Client_ID="your_app_id"');
        console.error('   export DINGTALK_Client_Secret="your_app_secret"');
        console.error('\n🔗 You can get these credentials from:');
        console.error('   https://open.dingtalk.com/');
        process.exit(1);
    }
}
/**
 * 显示配置信息
 */
function showConfiguration() {
    console.error('🚀 DingTalk MCP Server Configuration:');
    console.error(`   App ID: ${process.env.DINGTALK_Client_ID}`);
    console.error(`   App Secret: ${process.env.DINGTALK_Client_Secret ? '***' + process.env.DINGTALK_Client_Secret.slice(-4) : 'Not set'}`);
    console.error(`   Access Token: ${process.env.DINGTALK_ACCESS_TOKEN ? 'Provided' : 'Will be auto-generated'}`);
    console.error(`   Active Profiles: ${process.env.ACTIVE_PROFILES || 'Default'}`);
    console.error(`   Debug Mode: ${process.env.DEBUG ? 'Enabled' : 'Disabled'}`);
    console.error(`   Staging Mode: ${process.env.STAGING ? 'Enabled' : 'Disabled'}`);
    console.error('');
}
/**
 * 主函数
 */
async function main() {
    try {
        // 验证环境变量
        validateEnvironment();
        // 显示配置信息
        showConfiguration();
        // 创建并启动服务器
        const server = new DingTalkMCPServer();
        await server.run();
    }
    catch (error) {
        const err = error;
        console.error('❌ Failed to start DingTalk MCP Server:', err.message);
        if (err.message.includes('DINGTALK_Client_ID') || err.message.includes('DINGTALK_Client_Secret')) {
            console.error('\n💡 Make sure you have set the required environment variables:');
            console.error('   - DINGTALK_Client_ID: Your DingTalk application ID');
            console.error('   - DINGTALK_Client_Secret: Your DingTalk application secret');
        }
        process.exit(1);
    }
}
// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error.message);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// 启动服务器
main();
//# sourceMappingURL=cli.js.map