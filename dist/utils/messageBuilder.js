/**
 * 服务窗消息构建器
 */
export class ServiceWindowMessageBuilder {
    /**
     * 构建发送服务窗Markdown消息的请求体
     */
    static buildSendServiceWindowMarkdownBody(args) {
        return {
            userId: args.userId,
            accountId: args.accountId,
            messageBody: {
                msgtype: 'sampleMarkdown',
                sampleMarkdown: {
                    title: args.messageTitle,
                    text: args.messageContent
                }
            }
        };
    }
    /**
     * 构建批量发送服务窗Markdown消息的请求体
     */
    static buildBatchSendServiceWindowMarkdownBody(args) {
        return {
            userIdList: args.userIdList,
            accountId: args.accountId,
            messageBody: {
                msgtype: 'sampleMarkdown',
                sampleMarkdown: {
                    title: args.messageTitle,
                    text: args.messageContent
                }
            }
        };
    }
}
//# sourceMappingURL=messageBuilder.js.map