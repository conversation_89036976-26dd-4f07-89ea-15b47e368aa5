/**
 * 日志消息构建器
 */
export class LogMessageBuilder {
    /**
     * 构建日志请求体
     */
    static buildBody(args) {
        // 基本的日志构建逻辑
        const body = {
            template_id: args.template_id,
            contents: args.contents,
            userid: args.userid,
            to_chat: args.to_chat,
            dd_from: args.dd_from
        };
        // 可选参数
        if (args.to_userids) {
            body.to_userids = args.to_userids;
        }
        if (args.to_cids) {
            body.to_cids = args.to_cids;
        }
        return body;
    }
}
//# sourceMappingURL=logBuilder.js.map