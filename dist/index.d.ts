/**
 * DingTalk MCP Server - Main Entry Point
 *
 * This is the main entry point for the DingTalk Model Context Protocol (MCP) Server.
 * It provides integration between AI assistants and DingTalk's enterprise APIs.
 *
 * Features:
 * - Token caching and automatic refresh
 * - Support for multiple DingTalk API services
 * - Configurable tool loading via YAML files
 * - Environment-based configuration
 *
 * <AUTHOR> MCP Team
 * @version 1.0.0
 */
export { DingTalkMCPServer } from './DingTalkMCPServer.js';
export { ServiceWindowMessageBuilder } from './utils/messageBuilder.js';
export { LogMessageBuilder } from './utils/logBuilder.js';
export * from './types.js';
export type { MCPTool, MCPArg, RequestTemplate, HeaderTemplate, TokenCacheData, DingTalkTokenResponse, ToolExecutionResult, MCPServerConfig, MCPServerCapabilities, MCPToolSchema } from './types.js';
//# sourceMappingURL=index.d.ts.map