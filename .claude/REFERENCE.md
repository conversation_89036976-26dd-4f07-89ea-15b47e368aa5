# 钉钉MCP技术参考手册

## 📖 概述

本文档是钉钉MCP项目的技术参考手册，整合了代码规范、最佳实践、测试指南和故障排查等核心内容，为开发高质量MCP工具提供全面的技术指导。

---

## 🏗️ 代码结构规范

### 目录结构标准
```
dingtalk-mcp/
├── main.go                          # 程序入口点
├── internal/                        # 内部包，不对外暴露
│   └── service/                     # MCP服务层
│       ├── employee.go              # 员工相关服务
│       ├── message.go               # 消息相关服务
│       └── {feature}_service.go     # 其他功能服务
├── pkg/                             # 可重用的包
│   └── dingtalk/                    # 钉钉SDK封装
│       ├── dingtakl.go              # 主客户端
│       ├── employee.go              # 员工API封装
│       ├── message.go               # 消息API封装
│       ├── {feature}.go             # 其他功能API封装
│       ├── cache/                   # 缓存实现
│       ├── constant/                # 常量定义
│       │   ├── api.go               # API端点常量
│       │   └── genre.go             # 其他常量
│       ├── models/                  # 数据模型
│       ├── request/                 # 请求模型
│       └── response/                # 响应模型
```

### 命名规范

#### 文件和目录命名
- **Go源文件**: `snake_case` (如 `employee_service.go`)
- **测试文件**: 源文件名 + `_test.go` (如 `employee_service_test.go`)
- **目录名**: `lowercase` (如 `service`, `dingtalk`)

#### 代码命名规范
```go
// ✅ 正确的命名示例

// 包名：小写
package service

// 常量：PascalCase + Key后缀（API端点）
const GetEmployeeListKey = "/topapi/user/listsimple"

// 类型：PascalCase
type EmployeeService struct {
    client *dingtalk.DingTalk
}

// 方法：PascalCase，动词开头
func (svc *EmployeeService) GetEmployeeList() {}

// 变量：camelCase，缩写保持大写
var userID string
var httpClient *http.Client

// MCP工具名：snake_case
"get_employee_list"
```

#### JSON标签规范
```go
type Employee struct {
    UserID   string `json:"userid"`           // 小写+下划线
    Name     string `json:"name"`             // 简单字段
    DeptID   int    `json:"dept_id"`          // 下划线分隔
    Mobile   string `json:"mobile,omitempty"` // 可选字段
}
```

---

## 🛠️ 开发最佳实践

### 参数处理

#### 安全的类型断言
```go
// ✅ 推荐：安全的类型断言
func (svc *Service) HandleTool(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID, ok := req.Params.Arguments["user_id"].(string)
    if !ok {
        return nil, fmt.Errorf("user_id必须是字符串类型，实际类型: %T", req.Params.Arguments["user_id"])
    }
    
    if userID == "" {
        return nil, errors.New("user_id不能为空")
    }
    
    // 继续处理...
}

// ❌ 避免：直接类型断言可能导致panic
func (svc *Service) HandleTool(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID := req.Params.Arguments["user_id"].(string) // 可能panic
    // ...
}
```

#### 可选参数处理模式
```go
// API层：使用指针处理可选参数
func (ds *DingTalk) GetEmployeeList(deptID int, cursor *int, size *int) (*response.ListUserSimpleResponse, error) {
    body := map[string]interface{}{
        "dept_id": deptID,
    }
    
    if cursor != nil {
        body["cursor"] = *cursor
    } else {
        body["cursor"] = 0 // 默认值
    }
    
    if size != nil && *size > 0 && *size <= 100 {
        body["size"] = *size
    } else {
        body["size"] = 100 // 默认值
    }
    
    var data = &response.ListUserSimpleResponse{}
    return data, ds.Request(http.MethodPost, constant.GetEmployeeListKey, nil, body, data)
}

// MCP层：可选参数提取
func (svc *EmployeeService) GetEmployeeList(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    deptID, _ := req.Params.Arguments["dept_id"].(int) // 默认0
    
    var cursor, size *int
    if c, ok := req.Params.Arguments["cursor"].(int); ok && c >= 0 {
        cursor = &c
    }
    if s, ok := req.Params.Arguments["size"].(int); ok && s > 0 {
        size = &s
    }
    
    resp, err := svc.client.GetEmployeeList(deptID, cursor, size)
    // ...
}
```

#### 集中参数验证
```go
// 验证辅助函数
func validateUserIDs(userIDs string) error {
    if userIDs == "" {
        return errors.New("user_ids不能为空")
    }
    
    parts := strings.Split(userIDs, ",")
    if len(parts) > 100 {
        return errors.New("用户数量不能超过100个")
    }
    
    for _, part := range parts {
        if strings.TrimSpace(part) == "" {
            return errors.New("用户ID列表格式错误，不能包含空值")
        }
    }
    
    return nil
}

// 使用验证函数
func (svc *MessageService) SendMessage(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userIDs, ok := req.Params.Arguments["user_ids"].(string)
    if !ok {
        return nil, errors.New("user_ids必须是字符串类型")
    }
    
    if err := validateUserIDs(userIDs); err != nil {
        return nil, err
    }
    
    // 继续处理...
}
```

### 错误处理

#### 分层错误处理模式
```go
// Client层：包装底层错误，提供上下文
func (ds *DingTalk) GetEmployeeInfo(userID string) (*response.EmployeeResponse, error) {
    if userID == "" {
        return nil, errors.New("用户ID是必需的")
    }
    
    body := map[string]interface{}{"userid": userID}
    data := &response.EmployeeResponse{}
    
    if err := ds.Request(http.MethodPost, constant.GetUserDetailKey, nil, body, data); err != nil {
        return nil, fmt.Errorf("获取用户%s的信息失败: %w", userID, err)
    }
    
    return data, nil
}

// Service层：转换为用户友好的错误信息
func (svc *EmployeeService) GetEmployeeInfo(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID, ok := req.Params.Arguments["user_id"].(string)
    if !ok {
        return nil, errors.New("参数错误: user_id必须是字符串类型")
    }
    
    resp, err := svc.client.GetEmployeeInfo(userID)
    if err != nil {
        if strings.Contains(err.Error(), "60011") {
            return nil, fmt.Errorf("用户不存在: %s", userID)
        }
        return nil, fmt.Errorf("获取员工信息失败: %w", err)
    }
    
    result, _ := json.Marshal(resp.Result)
    return mcp.NewToolResultText(string(result)), nil
}
```

#### 常见钉钉错误码处理
```go
var dingtalkErrorMessages = map[string]string{
    "60011": "用户不存在",
    "60004": "权限不足",
    "90018": "请求频率超限，请稍后重试",
    "40014": "不合法的access_token",
    "40001": "不合法的请求字符",
}

func handleDingTalkError(err error) error {
    if err == nil {
        return nil
    }
    
    errStr := err.Error()
    for code, message := range dingtalkErrorMessages {
        if strings.Contains(errStr, code) {
            return errors.New(message)
        }
    }
    
    return fmt.Errorf("钉钉API调用失败: %w", err)
}
```

### 性能优化

#### 批量操作优化
```go
// 支持并发的批量操作
func (svc *EmployeeService) GetMultipleEmployees(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userIDsStr, ok := req.Params.Arguments["user_ids"].(string)
    if !ok {
        return nil, errors.New("user_ids必须是字符串类型")
    }
    
    userIDs := strings.Split(userIDsStr, ",")
    if len(userIDs) > 50 {
        return nil, errors.New("批量查询最多支持50个用户")
    }
    
    // 并发获取用户信息
    type result struct {
        index int
        data  interface{}
        err   error
    }
    
    resultChan := make(chan result, len(userIDs))
    
    for i, userID := range userIDs {
        go func(idx int, id string) {
            resp, err := svc.client.GetEmployeeInfo(strings.TrimSpace(id))
            if err != nil {
                resultChan <- result{idx, nil, err}
                return
            }
            resultChan <- result{idx, resp.Result, nil}
        }(i, userID)
    }
    
    // 收集结果
    results := make([]interface{}, len(userIDs))
    for i := 0; i < len(userIDs); i++ {
        res := <-resultChan
        if res.err != nil {
            return nil, fmt.Errorf("获取用户信息失败 (第%d个): %w", res.index+1, res.err)
        }
        results[res.index] = res.data
    }
    
    result, _ := json.Marshal(results)
    return mcp.NewToolResultText(string(result)), nil
}
```

#### 分页数据处理
```go
// 智能分页处理
func (svc *EmployeeService) GetAllEmployees(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    deptID, _ := req.Params.Arguments["dept_id"].(int)
    maxResults, _ := req.Params.Arguments["max_results"].(int)
    if maxResults <= 0 {
        maxResults = 1000 // 默认最大1000条
    }
    
    var allEmployees []response.SimpleEmployee
    cursor := 0
    pageSize := 100
    
    for len(allEmployees) < maxResults {
        resp, err := svc.client.GetSimpleEmployees(deptID, cursor, pageSize)
        if err != nil {
            return nil, fmt.Errorf("分页查询失败 (cursor=%d): %w", cursor, err)
        }
        
        allEmployees = append(allEmployees, resp.Result.List...)
        
        if !resp.Result.HasMore {
            break
        }
        
        cursor = resp.Result.NextCursor
        
        // 防止无限循环和内存溢出
        if len(allEmployees) >= maxResults {
            allEmployees = allEmployees[:maxResults]
            break
        }
    }
    
    result, _ := json.Marshal(map[string]interface{}{
        "employees":    allEmployees,
        "total_count":  len(allEmployees),
        "is_truncated": len(allEmployees) >= maxResults,
    })
    return mcp.NewToolResultText(string(result)), nil
}
```

---

## 🧪 测试指南

### 单元测试模板
```go
package service

import (
    "context"
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/mark3labs/mcp-go/mcp"
)

// Mock客户端
type MockDingTalkClient struct {
    mock.Mock
}

func (m *MockDingTalkClient) GetEmployeeInfo(userID string) (*response.EmployeeResponse, error) {
    args := m.Called(userID)
    return args.Get(0).(*response.EmployeeResponse), args.Error(1)
}

// 测试成功场景
func TestEmployeeService_GetEmployeeInfo_Success(t *testing.T) {
    mockClient := new(MockDingTalkClient)
    service := &EmployeeService{client: mockClient}
    
    expectedResp := &response.EmployeeResponse{
        Response: response.Response{Code: 0},
        Result: response.DetailEmployee{
            UserID: "test123",
            Name:   "张三",
        },
    }
    
    mockClient.On("GetEmployeeInfo", "test123").Return(expectedResp, nil)
    
    req := mcp.CallToolRequest{
        Params: mcp.CallToolRequestParams{
            Arguments: map[string]interface{}{
                "user_id": "test123",
            },
        },
    }
    
    result, err := service.GetEmployeeInfo(context.Background(), req)
    
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Contains(t, result.Content[0].Text, "test123")
    mockClient.AssertExpectations(t)
}

// 测试参数验证
func TestEmployeeService_GetEmployeeInfo_InvalidParam(t *testing.T) {
    service := &EmployeeService{}
    
    req := mcp.CallToolRequest{
        Params: mcp.CallToolRequestParams{
            Arguments: map[string]interface{}{
                "user_id": 123, // 错误类型
            },
        },
    }
    
    result, err := service.GetEmployeeInfo(context.Background(), req)
    
    assert.Error(t, err)
    assert.Nil(t, result)
    assert.Contains(t, err.Error(), "必须是字符串类型")
}

// 测试API错误处理
func TestEmployeeService_GetEmployeeInfo_APIError(t *testing.T) {
    mockClient := new(MockDingTalkClient)
    service := &EmployeeService{client: mockClient}
    
    mockClient.On("GetEmployeeInfo", "invalid").Return(nil, errors.New("API error"))
    
    req := mcp.CallToolRequest{
        Params: mcp.CallToolRequestParams{
            Arguments: map[string]interface{}{
                "user_id": "invalid",
            },
        },
    }
    
    result, err := service.GetEmployeeInfo(context.Background(), req)
    
    assert.Error(t, err)
    assert.Nil(t, result)
    mockClient.AssertExpectations(t)
}
```

### 集成测试模板
```go
// +build integration

func TestEmployeeService_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过集成测试")
    }
    
    client := dingtalk.NewClient(
        os.Getenv("DINGTALK_AGENT_ID"),
        os.Getenv("DINGTALK_KEY"),
        os.Getenv("DINGTALK_SECRET"),
    )
    
    service := NewEmployeeService(client)
    
    req := mcp.CallToolRequest{
        Params: mcp.CallToolRequestParams{
            Arguments: map[string]interface{}{
                "dept_id": 1,
            },
        },
    }
    
    result, err := service.GetEmployeeList(context.Background(), req)
    assert.NoError(t, err)
    assert.NotNil(t, result)
    
    // 验证返回数据格式
    var data map[string]interface{}
    err = json.Unmarshal([]byte(result.Content[0].Text), &data)
    assert.NoError(t, err)
    assert.Contains(t, data, "employees")
}
```

---

## 🔍 故障排查指南

### 常见问题诊断

#### 编译错误
```bash
# 问题：依赖缺失
go mod tidy

# 问题：包导入错误
go mod verify

# 问题：版本冲突
go mod graph | grep conflicting_package
```

#### 运行时错误

**1. Access Token相关**
```bash
# 检查环境变量
echo $DINGTALK_AGENT_ID
echo $DINGTALK_KEY  
echo $DINGTALK_SECRET

# 检查Token缓存
ls -la ~/.dingtalk_cache/

# 手动清除缓存
rm -rf ~/.dingtalk_cache/
```

**2. API调用失败**
```go
// 添加调试日志
func (ds *DingTalk) Request(method, path string, query url.Values, body interface{}, data response.Unmarshalled) error {
    log.Printf("API调用: %s %s, body: %+v", method, path, body)
    
    // ... 原有逻辑
    
    log.Printf("API响应: %+v", data)
    return nil
}
```

**3. MCP工具无法识别**
```go
// 检查工具注册
func main() {
    svc := server.NewMCPServer("dingtalk-mcp", "1.0.0")
    
    // 添加调试信息
    log.Println("注册的工具:")
    service.NewEmployeeService(client).AddTools(svc)
    service.NewMessageService(client).AddTools(svc)  
    // 确保这里包含了你的新服务
    
    svc.Serve()
}
```

### 调试技巧

#### 结构化日志
```go
// 统一的日志格式
type Logger struct {
    service string
}

func (l *Logger) Info(msg string, fields ...interface{}) {
    log.Printf("[%s] %s %v", l.service, msg, fields)
}

func (l *Logger) Error(msg string, err error, fields ...interface{}) {
    log.Printf("[%s] ERROR: %s - %v %v", l.service, msg, err, fields)
}

// 使用示例
var logger = &Logger{service: "employee"}

func (svc *EmployeeService) GetEmployeeInfo(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID := req.Params.Arguments["user_id"].(string)
    logger.Info("获取员工信息", "user_id", userID)
    
    resp, err := svc.client.GetEmployeeInfo(userID)
    if err != nil {
        logger.Error("API调用失败", err, "user_id", userID)
        return nil, err
    }
    
    logger.Info("获取员工信息成功", "user_id", userID, "name", resp.Result.Name)
    // ...
}
```

#### 性能监控
```go
// 性能监控装饰器
func withPerformanceMonitoring(toolName string, handler func(context.Context, mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(context.Context, mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    return func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
        start := time.Now()
        
        result, err := handler(ctx, req)
        
        duration := time.Since(start)
        
        log.Printf("工具执行: %s, 耗时: %v, 成功: %t", toolName, duration, err == nil)
        
        if duration > 5*time.Second {
            log.Printf("SLOW_QUERY: %s 执行时间过长: %v", toolName, duration)
        }
        
        return result, err
    }
}

// 使用装饰器
func (svc *EmployeeService) AddTools(server *server.MCPServer) {
    tool := mcp.NewTool("get_employee_info", /* ... */)
    server.AddTool(tool, withPerformanceMonitoring("get_employee_info", svc.GetEmployeeInfo))
}
```

### 部署配置

#### 环境变量配置
```bash
# 必需的环境变量
export DINGTALK_AGENT_ID="your_agent_id"
export DINGTALK_KEY="your_app_key"
export DINGTALK_SECRET="your_app_secret"

# 可选的配置
export DINGTALK_CACHE_DIR="~/.dingtalk_cache"
export DINGTALK_LOG_LEVEL="info"
export DINGTALK_TIMEOUT="30s"
```

#### Claude Desktop集成
```json
// ~/.claude_desktop_config.json
{
  "mcpServers": {
    "dingtalk": {
      "command": "/path/to/dingtalk-mcp",
      "env": {
        "DINGTALK_AGENT_ID": "your_agent_id",
        "DINGTALK_KEY": "your_app_key",
        "DINGTALK_SECRET": "your_app_secret"
      }
    }
  }
}
```

---

## 📚 参考资源

### 钉钉API文档
- [开发者文档](https://open.dingtalk.com/document/)
- [API参考](https://open.dingtalk.com/document/orgapp/api-overview)
- [错误码说明](https://open.dingtalk.com/document/orgapp/api-error-code)

### Go开发资源
- [Go最佳实践](https://golang.org/doc/effective_go)
- [测试指南](https://golang.org/doc/tutorial/add-a-test)
- [包设计原则](https://golang.org/doc/code)

### MCP协议
- [MCP规范](https://spec.modelcontextprotocol.io/)
- [mcp-go库文档](https://github.com/mark3labs/mcp-go)

---

遵循本参考手册的指导，可以确保开发出高质量、可维护的钉钉MCP工具。