# 钉钉MCP工具生成器

根据钉钉API文档生成完整的MCP工具代码

## 生成要求

API文档内容路径:$ARGUMENTS。

1. 首先读取指定路径的Markdown文档内容
2. 仔细分析和理解文档的完整内容，包括：
   - API接口定义和参数说明
   - 使用示例和代码片段
   - 配置要求和依赖关系
   - 错误处理和最佳实践
3. 基于对文档内容的全面理解后，再开始进行相关的开发工作
4. 确保开发实现严格遵循文档中的规范和要求

注意：必须完整阅读并理解文档内容后才能开始编码，不能仅基于部分信息或假设进行开发

基于提供的API文档，生成符合钉钉MCP项目标准的完整代码，包括：

1. **常量定义** - 添加到 `pkg/dingtalk/constant/api.go`
2. **响应结构** - 创建 `pkg/dingtalk/response/[feature].go`
3. **API客户端** - 创建或扩展 `pkg/dingtalk/[feature].go`
4. **MCP服务** - 创建 `internal/service/[feature]_service.go`
5. **服务注册** - 在 `main.go` 中添加注册代码

## 项目架构约定

### 目录结构
```
dingtalk-mcp/
├── main.go                          # 程序入口
├── internal/service/                # MCP服务层
├── pkg/dingtalk/                    # 钉钉客户端封装
│   ├── constant/api.go              # API端点常量
│   ├── response/                    # 响应结构
│   └── [feature].go                 # API实现
```

### 基础设施
```go
// 统一响应基类
type Response struct {
    Code      int    `json:"errcode"`
    Msg       string `json:"errmsg,omitempty"`
    Success   bool   `json:"success,omitempty"`
    RequestId string `json:"request_id,omitempty"`
}

// 钉钉客户端调用方法
func (ds *DingTalk) Request(method, path string, query url.Values, body interface{}, data response.Unmarshalled) error

// MCP工具处理签名
func (svc *Service) ToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error)
```

## 代码生成模板

### 1. 常量定义
```go
const (
    YourApiEndpointKey = "/api/endpoint/path" // API功能描述
)
```

### 2. 响应结构
```go
package response

type YourApiResponse struct {
    Response
    Result YourResultStruct `json:"result"`
}

type YourResultStruct struct {
    Field1 string `json:"field1"`
    Field2 int    `json:"field2"`
}
```

### 3. API客户端方法
```go
// YourApiMethod API功能的中文描述
func (ds *DingTalk) YourApiMethod(param1 string, param2 int) (*response.YourApiResponse, error) {
    var (
        body = map[string]interface{}{
            "param1": param1,
            "param2": param2,
        }
        data = &response.YourApiResponse{}
    )
    
    if err := ds.Request(http.MethodPost, constant.YourApiEndpointKey, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

### 4. MCP服务实现
```go
package service

type YourService struct {
    client *dingtalk.DingTalk
}

func NewYourService(client *dingtalk.DingTalk) *YourService {
    return &YourService{client: client}
}

// YourToolMethod 工具功能的中文描述
func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 参数提取和验证
    param1, ok := req.Params.Arguments["param1"].(string)
    if !ok {
        return nil, errors.New("param1必须是字符串类型")
    }
    
    // 2. 调用钉钉API
    resp, err := svc.client.YourApiMethod(param1, param2)
    if err != nil {
        return nil, fmt.Errorf("调用钉钉API失败: %w", err)
    }
    
    // 3. 返回结果
    result, _ := json.Marshal(resp.Result)
    return mcp.NewToolResultText(string(result)), nil
}

// AddTools 注册MCP工具
func (svc *YourService) AddTools(server *server.MCPServer) {
    tool := mcp.NewTool("your_tool_name",
        mcp.WithDescription("工具功能的详细中文描述"),
        mcp.WithString("param1",
            mcp.Required(),
            mcp.Description("参数1的详细描述")))
    
    server.AddTool(tool, svc.YourToolMethod)
}
```

### 5. 主程序注册
```go
// 在main.go中添加
service.NewYourService(client).AddTools(svc)
```

## 编码规范

### 命名约定
- **常量名**: `YourApiEndpointKey` (PascalCase + Key后缀)
- **结构体**: `YourApiResponse`, `YourService` (PascalCase)
- **方法名**: `YourApiMethod` (PascalCase)
- **文件名**: `your_service.go` (snake_case)
- **MCP工具名**: `your_tool_name` (snake_case)
- **变量名**: `userID` (camelCase，缩写保持大写)

### 质量要求
- ✅ 完整的中文注释
- ✅ 全面的参数验证和错误处理
- ✅ 详细的工具描述和参数说明
- ✅ 完整映射API文档的所有字段

## 高级特性支持

### 可选参数处理
```go
// API方法中使用指针
func (ds *DingTalk) ApiWithOptional(required string, optional *int) error

// MCP工具中的处理
var optional *int
if val, ok := req.Params.Arguments["optional"].(int); ok {
    optional = &val
}
```

### 分页数据处理
```go
// 包含分页信息的响应
result := map[string]interface{}{
    "data":        resp.Result.List,
    "has_more":    resp.Result.HasMore,
    "next_cursor": resp.Result.NextCursor,
}
```

### 批量操作限制
```go
if len(itemList) > 100 {
    return nil, errors.New("批量操作最多支持100个项目")
}
```

---

**执行时请参考 `.claude/REFERENCE.md` 获取详细的最佳实践、测试指南和故障排查信息。**