# dingtalk-mcp

本项目是一个钉钉MCP（Message Connector Protocol）服务，提供了与钉钉企业应用交互的API接口。项目基于Go语言开发，支持员工信息查询和消息发送等功能。

## 📚 开发文档

项目文档已经过精简和整合，位于 `a-docs/` 目录：

- **[项目架构分析](a-docs/01-项目架构分析.md)** - 了解项目结构和核心组件
- **[AI开发模板](a-docs/02-AI开发模板.md)** - 使用AI快速开发新工具的完整模板
- **[质量保证模板](a-docs/03-质量保证模板.md)** - 参数验证、错误处理和测试模板
- **[文档使用说明](a-docs/README.md)** - 详细的文档结构和使用指南

### 🚀 快速开发新工具

1. 阅读 [项目架构分析](a-docs/01-项目架构分析.md) 了解项目结构
2. 使用 [AI开发模板](a-docs/02-AI开发模板.md) 中的提示词模板
3. 提供钉钉API文档给AI助手，自动生成完整代码
4. 使用 [质量保证模板](a-docs/03-质量保证模板.md) 进行代码检查

# 安装

```bash
go install github.com/zhaoyunxing92/dingtalk-mcp@latest
```

## 配置MCP服务

>  [钉钉开放平台](https://open-dev.dingtalk.com) 创建一个应用，并给应用配置权限

```json
{
    "mcpServers": {
       "dingtalk": {
            "command": "dingtalk-mcp", // 如果提示找不到命令，可以将项目编译后的可执行文件放在PATH中
            "args": [],
            "env": {
                "DINGTALK_AGENT_ID": "申请的agentId",
                "DINGTALK_KEY": "应用key",
                "DINGTALK_SECRET": "应用密钥"
            },
            "disabled": false,
            "autoApprove": [
                "get_employees_count",
                "get_simple_employees",
                "recall_corp_conversation",
                "send_corp_conversation",
                "send_markdown_corp_conversation"
            ],
            "timeout": 60
        }
    }
  }
```


## 功能列表

| API名称 | 功能描述 |
|---------|----------|
| get_employees_count | 获取企业员工人数 |
| get_simple_employees | 获取企业的员工基础信息(只获取根部门的人) |
| recall_corp_conversation | 撤回给员工的消息 |
| send_corp_conversation | 企业用户发送文本消息 |
| send_markdown_corp_conversation | 企业用户发送Markdown格式消息 |

## 📖 文档更新说明

项目文档已进行重大整合优化：
- **从10个文档精简为3个核心文档**，大幅降低维护成本
- **消除内容重复**，提供更清晰的学习路径
- **新增AI开发模板**，支持快速自动化开发新工具
- **整合质量保证流程**，确保代码质量和一致性

详细的整合说明请查看 [a-docs/README.md](a-docs/README.md)。
