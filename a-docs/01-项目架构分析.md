# 钉钉MCP项目架构分析

## 1. 项目整体架构

### 1.1 分层架构设计
项目采用清晰的分层架构模式：

```
dingtalk-mcp/
├── main.go                          # 程序入口，MCP服务器初始化
├── internal/service/                # MCP服务层：处理MCP工具调用
│   ├── employee.go                  # 员工服务
│   ├── message.go                   # 消息服务
│   └── department.go                # 部门服务（未完成）
├── pkg/dingtalk/                    # 钉钉客户端层：封装API调用
│   ├── dingtakl.go                  # 主客户端
│   ├── employee.go                  # 员工API封装
│   ├── message.go                   # 消息API封装
│   ├── constant/api.go              # API端点常量定义
│   ├── response/                    # 响应结构定义
│   ├── models/                      # 数据模型定义
│   └── cache/                       # 缓存系统
```

### 1.2 核心组件

#### 1.2.1 主程序入口 (main.go)
- **功能**：MCP服务器初始化和配置管理
- **配置方式**：支持命令行参数和环境变量
- **服务注册**：通过 `AddTools()` 方法注册各个服务的工具

#### 1.2.2 钉钉客户端 (pkg/dingtalk/dingtakl.go)
- **认证机制**：自动获取和缓存access_token
- **API版本支持**：自动区分新旧API版本（v1.0/v2.0 vs 传统API）
- **统一请求方法**：`Request(method, path, query, body, response)`
- **错误处理**：统一的响应结构和错误检查机制

#### 1.2.3 缓存系统 (pkg/dingtalk/cache/)
- **接口设计**：定义了统一的Cache接口
- **实现方式**：支持内存缓存和文件缓存
- **过期机制**：自动处理token过期（提前60秒刷新）

## 2. MCP工具注册机制

### 2.1 注册流程
1. 每个服务实现 `AddTools(svc *server.MCPServer)` 方法
2. 使用 `mcp.NewTool()` 创建工具定义
3. 通过 `svc.AddTool()` 注册工具和处理函数

### 2.2 工具处理函数签名
```go
func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error)
```

### 2.3 参数定义模式
- 使用链式调用定义参数：`mcp.WithString()`, `mcp.WithBoolean()` 等
- 支持必需参数：`mcp.Required()`
- 支持参数描述：`mcp.Description()`

## 3. 钉钉API集成模式

### 3.1 API端点管理
- **常量定义**：所有API端点在 `constant/api.go` 中统一定义
- **命名规范**：使用 `XxxKey` 后缀，如 `GetUserCountKey`
- **版本区分**：自动识别新旧API版本

### 3.2 请求处理流程
1. **Token管理**：自动添加access_token到请求中
2. **版本判断**：根据路径前缀判断API版本
3. **请求构建**：支持GET/POST请求，自动处理JSON序列化
4. **响应解析**：统一的响应结构和错误检查

### 3.3 响应结构设计
- **基础响应**：所有响应继承 `Response` 结构
- **错误检查**：实现 `Unmarshalled` 接口的 `CheckError` 方法
- **结果封装**：使用 `Result` 字段包装实际数据

## 4. 命名规范

### 4.1 命名规则表
| 类型 | 规则 | 示例 |
|------|------|------|
| API常量 | PascalCase + Key | `GetUserListKey` |
| 响应结构 | PascalCase + Response | `GetUserListResponse` |
| 结果结构 | PascalCase | `UserListResult` |
| 服务结构 | PascalCase + Service | `UserService` |
| 客户端方法 | PascalCase | `GetUserList` |
| 服务方法 | PascalCase | `GetUserList` |
| MCP工具名 | snake_case | `get_user_list` |
| 文件名 | snake_case | `user_service.go` |

### 4.2 配置管理
- **AgentId**：钉钉应用的Agent ID
- **AppKey**：应用密钥  
- **AppSecret**：应用秘钥
- **配置方式**：命令行参数或环境变量

## 5. 技术栈

- **语言**：Go 1.24+
- **MCP框架**：github.com/mark3labs/mcp-go
- **HTTP客户端**：标准库 net/http
- **JSON处理**：标准库 encoding/json
- **错误处理**：github.com/pkg/errors
