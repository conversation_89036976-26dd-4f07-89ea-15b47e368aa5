# 钉钉MCP工具质量保证模板

## 1. 参数验证模板

### 1.1 类型安全的参数提取

#### 字符串参数提取
```go
// 必需字符串参数
paramName, ok := req.Params.Arguments["param_name"].(string)
if !ok {
    return nil, errors.New("param_name必须是字符串类型")
}

// 可选字符串参数
paramName := ""
if val, ok := req.Params.Arguments["param_name"].(string); ok {
    paramName = val
} else if req.Params.Arguments["param_name"] != nil {
    return nil, errors.New("param_name必须是字符串类型")
}
```

#### 整数参数提取
```go
// 必需整数参数（处理JSON数字默认为float64的情况）
paramName, ok := req.Params.Arguments["param_name"].(int)
if !ok {
    if floatVal, ok := req.Params.Arguments["param_name"].(float64); ok {
        paramName = int(floatVal)
    } else {
        return nil, errors.New("param_name必须是整数类型")
    }
}

// 可选整数参数（带默认值）
paramName := 20 // 默认值
if val, ok := req.Params.Arguments["param_name"].(int); ok {
    paramName = val
} else if floatVal, ok := req.Params.Arguments["param_name"].(float64); ok {
    paramName = int(floatVal)
} else if req.Params.Arguments["param_name"] != nil {
    return nil, errors.New("param_name必须是整数类型")
}
```

#### 布尔参数提取
```go
// 必需布尔参数
paramName, ok := req.Params.Arguments["param_name"].(bool)
if !ok {
    return nil, errors.New("param_name必须是布尔类型")
}

// 可选布尔参数（带默认值）
paramName := false // 默认值
if val, ok := req.Params.Arguments["param_name"].(bool); ok {
    paramName = val
} else if req.Params.Arguments["param_name"] != nil {
    return nil, errors.New("param_name必须是布尔类型")
}
```

### 1.2 业务逻辑验证

#### 非空验证
```go
// 字符串非空验证
if strings.TrimSpace(paramName) == "" {
    return nil, errors.New("param_name不能为空")
}

// 数组非空验证
if len(paramArray) == 0 {
    return nil, errors.New("param_array不能为空数组")
}
```

#### 长度验证
```go
// 字符串长度验证
if len(paramName) > 100 {
    return nil, errors.New("param_name长度不能超过100个字符")
}

if len(paramName) < 1 {
    return nil, errors.New("param_name长度不能少于1个字符")
}

// 数组长度验证
if len(paramArray) > 50 {
    return nil, errors.New("param_array数组长度不能超过50个元素")
}
```

#### 数值范围验证
```go
// 整数范围验证
if paramName < 1 {
    return nil, errors.New("param_name不能小于1")
}

if paramName > 1000 {
    return nil, errors.New("param_name不能大于1000")
}

// 组合范围验证
if paramName < 1 || paramName > 100 {
    return nil, fmt.Errorf("param_name必须在1到100之间，当前值: %d", paramName)
}
```

#### 格式验证
```go
// 邮箱格式验证
emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
if !emailRegex.MatchString(email) {
    return nil, errors.New("email格式不正确，请输入有效的邮箱地址")
}

// 手机号格式验证
phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
if !phoneRegex.MatchString(phone) {
    return nil, errors.New("phone格式不正确，请输入有效的手机号码")
}

// 用户ID格式验证（钉钉用户ID通常是数字字符串）
if !regexp.MustCompile(`^\d+$`).MatchString(userID) {
    return nil, errors.New("user_id格式不正确，用户ID应该是数字字符串")
}
```

#### 枚举值验证
```go
// 字符串枚举验证
validValues := []string{"text", "markdown", "image"}
valid := false
for _, v := range validValues {
    if messageType == v {
        valid = true
        break
    }
}
if !valid {
    return nil, fmt.Errorf("message_type必须是以下值之一: %s", strings.Join(validValues, ", "))
}

// 使用辅助函数的枚举验证
func contains(slice []string, item string) bool {
    for _, s := range slice {
        if s == item {
            return true
        }
    }
    return false
}

if !contains(validValues, messageType) {
    return nil, fmt.Errorf("message_type必须是以下值之一: %s", strings.Join(validValues, ", "))
}
```

## 2. 错误处理模板

### 2.1 分层错误处理

#### 参数层错误
```go
// 参数类型错误
return nil, fmt.Errorf("参数%s类型错误：期望%s，实际%T", paramName, expectedType, actualValue)

// 参数值错误
return nil, fmt.Errorf("参数%s值错误：%s", paramName, errorDetails)

// 参数缺失错误
return nil, fmt.Errorf("缺少必需参数：%s", paramName)
```

#### API调用层错误
```go
// API调用失败
resp, err := svc.client.ApiMethod(parameters)
if err != nil {
    return nil, fmt.Errorf("调用钉钉API失败: %w", err)
}

// API响应错误检查
if resp.Code != 0 {
    return nil, fmt.Errorf("钉钉API返回错误 (错误码: %d): %s", resp.Code, resp.Msg)
}
```

#### 数据处理层错误
```go
// JSON序列化错误
if marshal, err := json.Marshal(resp.Result); err != nil {
    return nil, fmt.Errorf("响应数据序列化失败: %w", err)
} else {
    return mcp.NewToolResultText(string(marshal)), nil
}

// 数据转换错误
if convertedValue, err := convertData(rawValue); err != nil {
    return nil, fmt.Errorf("数据转换失败: %w", err)
}
```

### 2.2 用户友好的错误信息

#### 错误消息常量
```go
const (
    // 参数错误消息
    ErrParamRequired     = "%s为必需参数"
    ErrParamType         = "%s必须是%s类型"
    ErrParamEmpty        = "%s不能为空"
    ErrParamTooLong      = "%s长度不能超过%d个字符"
    ErrParamTooShort     = "%s长度不能少于%d个字符"
    ErrParamOutOfRange   = "%s必须在%d到%d之间"
    ErrParamInvalidValue = "%s必须是以下值之一: %s"
    ErrParamInvalidFormat = "%s格式不正确"
    
    // 业务错误消息
    ErrResourceNotFound  = "资源不存在: %s"
    ErrPermissionDenied  = "权限不足: %s"
    ErrBusinessRule      = "业务规则验证失败: %s"
    
    // API错误消息
    ErrApiCall           = "调用钉钉API失败: %s"
    ErrApiResponse       = "钉钉API返回错误 (错误码: %d): %s"
    ErrDataSerialization = "响应数据序列化失败: %s"
)
```

#### 错误消息使用
```go
// 使用错误消息常量
if paramName == "" {
    return nil, fmt.Errorf(ErrParamEmpty, "参数名称")
}

if paramName < minValue || paramName > maxValue {
    return nil, fmt.Errorf(ErrParamOutOfRange, "参数名称", minValue, maxValue)
}
```

## 3. 基础测试模板

### 3.1 参数验证测试
```go
func TestService_ParameterValidation(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := NewService(client)
    
    tests := []struct {
        name    string
        args    map[string]interface{}
        wantErr bool
        errMsg  string
    }{
        {
            name: "正常参数",
            args: map[string]interface{}{
                "param1": "valid_value",
                "param2": 100,
            },
            wantErr: false,
        },
        {
            name: "缺少必需参数",
            args: map[string]interface{}{
                "param2": 100,
            },
            wantErr: true,
            errMsg:  "param1必须是字符串类型",
        },
        {
            name: "参数类型错误",
            args: map[string]interface{}{
                "param1": 123, // 应该是字符串
                "param2": 100,
            },
            wantErr: true,
            errMsg:  "param1必须是字符串类型",
        },
        {
            name: "参数值无效",
            args: map[string]interface{}{
                "param1": "", // 空字符串
                "param2": 100,
            },
            wantErr: true,
            errMsg:  "param1不能为空",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            req := mcp.CallToolRequest{
                Params: mcp.CallToolRequestParams{
                    Arguments: tt.args,
                },
            }
            
            result, err := service.MethodName(context.Background(), req)
            
            if tt.wantErr {
                assert.Error(t, err)
                if tt.errMsg != "" {
                    assert.Contains(t, err.Error(), tt.errMsg)
                }
                assert.Nil(t, result)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, result)
            }
        })
    }
}
```

### 3.2 响应格式化测试
```go
func TestService_ResponseFormatting(t *testing.T) {
    tests := []struct {
        name     string
        input    interface{}
        expected string
        wantErr  bool
    }{
        {
            name: "简单对象",
            input: map[string]interface{}{
                "id":   "123",
                "name": "测试用户",
            },
            expected: `{"id":"123","name":"测试用户"}`,
            wantErr:  false,
        },
        {
            name: "复杂嵌套对象",
            input: map[string]interface{}{
                "user": map[string]interface{}{
                    "id":   "123",
                    "name": "测试用户",
                },
                "department": map[string]interface{}{
                    "id":   "456",
                    "name": "测试部门",
                },
            },
            wantErr: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            marshal, err := json.Marshal(tt.input)
            
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                if tt.expected != "" {
                    assert.JSONEq(t, tt.expected, string(marshal))
                }
            }
        })
    }
}
```

## 4. 代码质量检查

### 4.1 代码检查清单
- [ ] 所有函数和结构体有中文注释
- [ ] 参数提取使用类型安全检查
- [ ] 参数验证完整（类型+业务逻辑）
- [ ] 错误处理合理（分层+用户友好）
- [ ] 响应格式化正确
- [ ] 工具注册完成
- [ ] 命名规范一致
- [ ] 代码结构清晰

### 4.2 自动化检查
```bash
# 代码格式检查
gofmt -d .

# 代码质量检查
golint ./...
go vet ./...

# 编译检查
go build -v ./...

# 单元测试
go test -v -short ./...

# 覆盖率测试
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```
